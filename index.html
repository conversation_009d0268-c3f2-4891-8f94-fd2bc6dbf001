<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0">
  <title>Bollineni Narendra Chowdary | Web Developer & AI Explorer</title>
  <meta name="description" content="Portfolio of Bollineni Narendra Chowdary, a Computer Science graduate specializing in web development, AI tools, and innovative digital solutions.">
  <meta name="keywords" content="<PERSON><PERSON><PERSON>, Web Developer, AI Explorer, Portfolio, React, JavaScript, Tailwind CSS, Frontend Developer">
  <meta name="author" content="Bollineni Narendra Chowdary">

  <!-- Open Graph / Social Media Meta Tags -->
  <meta property="og:title" content="Bollineni Narendra Chowdary | Web Developer & AI Explorer">
  <meta property="og:description" content="Portfolio of Bollineni Narendra Chowdary, a Computer Science graduate specializing in web development, AI tools, and innovative digital solutions.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://your-portfolio-url.com">

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='6'%3E%3Cpath d='M 50 21 L 79 40 L 50 60 L 21 40 Z'/%3E%3Cpath d='M 50 40 L 79 59 L 50 79 L 21 59 Z'/%3E%3C/g%3E%3C/svg%3E">

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Stylesheets -->
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="css/sections.css">
  <link rel="stylesheet" href="css/home.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/projects.css">
  <link rel="stylesheet" href="css/macos-interface.css">
  <link rel="stylesheet" href="css/experience.css">
  <link rel="stylesheet" href="css/contact.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Black Landing Screen with Glowing Effect -->
    <div class="landing-screen" id="landing">
        <div class="background-effect"></div>
        <div class="message-container">
            <div class="message">Hello</div>
            <div class="message">నమస్కారం</div> <!-- Telugu -->
            <div class="message">नमस्ते</div> <!-- Hindi -->
            <div class="message">வணக்கம்</div> <!-- Tamil -->
            <div class="message">ನಮಸ್ಕಾರ</div> <!-- Kannada -->
            <div class="message">നമസ്കാരം</div> <!-- Malayalam -->
            <div class="message">નમસ્તે</div> <!-- Gujarati -->
            <div class="message">ନମସ୍କାର</div> <!-- Odia -->
            <div class="message">السلام علیکم</div> <!-- Urdu -->
            <div class="message">こんにちは</div> <!-- Japanese -->
            <div class="message">안녕하세요</div> <!-- Korean -->
            <div class="message">Здравствуйте</div> <!-- Russian -->
            <div class="message">Vanakkam</div> <!-- Sri Lankan Tamil -->
        </div>
    </div>

    <div class="cursor-dot"></div>

    <!-- New Modern Navbar -->
    <header class="navbar-header">
        <div class="navbar-container">
            <div class="navbar-logo">
                <svg viewBox="0 0 100 100" class="logo-svg">
                    <g fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="6">
                        <!-- left line -->
                        <path d="M 21 40 V 59">
                            <animateTransform attributeName="transform" attributeType="XML" type="rotate" values="0 21 59; 180 21 59" dur="2s" repeatCount="indefinite" />
                        </path>
                        <!-- right line -->
                        <path d="M 79 40 V 59">
                            <animateTransform attributeName="transform" attributeType="XML" type="rotate" values="0 79 59; -180 79 59" dur="2s" repeatCount="indefinite" />
                        </path>
                        <!-- top line -->
                        <path d="M 50 21 V 40">
                            <animate attributeName="d" values="M 50 21 V 40; M 50 59 V 40" dur="2s" repeatCount="indefinite" />
                        </path>
                        <!-- btm line -->
                        <path d="M 50 60 V 79">
                            <animate attributeName="d" values="M 50 60 V 79; M 50 98 V 79" dur="2s" repeatCount="indefinite" />
                        </path>
                        <!-- top box -->
                        <path d="M 50 21 L 79 40 L 50 60 L 21 40 Z">
                            <animate attributeName="stroke" values="rgba(0,0,0,1); rgba(100,100,100,0)" dur="2s" repeatCount="indefinite" />
                        </path>
                        <!-- mid box -->
                        <path d="M 50 40 L 79 59 L 50 79 L 21 59 Z" />
                        <!-- btm box -->
                        <path d="M 50 59 L 79 78 L 50 98 L 21 78 Z">
                            <animate attributeName="stroke" values="rgba(100,100,100,0); rgba(0,0,0,1)" dur="2s" repeatCount="indefinite" />
                        </path>
                        <animateTransform attributeName="transform" attributeType="XML" type="translate" values="0 0; 0 -19" dur="2s" repeatCount="indefinite" />
                    </g>
                </svg>
                <span class="navbar-name">Narendra Chowdary</span>
            </div>

            <!-- Desktop Navigation -->
            <nav class="desktop-nav">
                <button data-section="home" class="nav-link active">home</button>
                <button data-section="about" class="nav-link">about</button>
                <button data-section="projects" class="nav-link">projects</button>
                <button data-section="experience" class="nav-link">experience</button>
                <button data-section="contact" class="nav-link">contact</button>
            </nav>

            <div class="navbar-actions">
                <!-- Theme toggle - Keep existing toggle -->
                <div class="switch">
                    <input type="checkbox" id="toggle">
                    <label for="toggle">
                        <i class="bulb">
                            <span class="bulb-center"></span>
                            <span class="filament-1"></span>
                            <span class="filament-2"></span>
                            <span class="reflections">
                                <span></span>
                            </span>
                            <span class="sparks">
                                <i class="spark1"></i>
                                <i class="spark2"></i>
                                <i class="spark3"></i>
                                <i class="spark4"></i>
                                <i class="spark5"></i>
                                <i class="spark6"></i>
                                <i class="spark7"></i>
                                <i class="spark8"></i>
                            </span>
                        </i>
                    </label>
                </div>

                <!-- Mobile menu button -->
                <button class="mobile-menu-btn" aria-label="Toggle mobile menu">
                    <i class="fas fa-bars menu-icon"></i>
                    <i class="fas fa-times close-icon hidden"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Mobile Navigation Menu -->
    <div class="mobile-nav">
        <nav>
            <button data-section="home" class="nav-link active">home</button>
            <button data-section="about" class="nav-link">about</button>
            <button data-section="projects" class="nav-link">projects</button>
            <button data-section="experience" class="nav-link">experience</button>
            <button data-section="contact" class="nav-link">contact</button>
        </nav>
    </div>

    <!-- Main content -->
    <div class="introduction" id="home">
        <div class="text-container">
            <div class="online">
                <span class="status-dot"></span>
                Available for work
            </div>
            <h1 class="intro-heading">Hi, I'm Narendra <span class="wave-emoji">👋</span></h1>
            <div class="bio-container">
                <h2 class="bio-title">No-Code Developer | AI Prompt Engineer | Mobile App Creator</h2>
                <p class="bio-text">Recent Computer Science and Engineering graduate specializing in AI-assisted development and no-code solutions. I leverage modern AI tools to build efficient SaaS applications, mobile apps, and automation workflows. With expertise in cloud backends and API integration, I create cost-effective digital solutions with minimal traditional coding.</p>
            </div>
            <div class="button-container">
                <button class="button get-in-touch">
                    <span>Get in Touch</span>
                    <i class="fas fa-arrow-right"></i>
                </button>
                <button class="button view-projects">
                    <span>View Projects</span>
                    <i class="fas fa-code"></i>
                </button>
            </div>
            <div class="social-icons">
                <a href="https://github.com/nrenx" class="social-icon github" target="_blank" rel="noopener noreferrer">
                    <i class="fab fa-github"></i>
                </a>
                <a href="https://linkedin.com/in/bollineninarendrachowdary" class="social-icon linkedin" target="_blank" rel="noopener noreferrer">
                    <i class="fab fa-linkedin"></i>
                </a>
                <a href="#" class="social-icon twitter" target="_blank" rel="noopener noreferrer">
                    <i class="fab fa-twitter"></i>
                </a>
            </div>
        </div>
        <div class="image-container">
            <div class="morph-container">
                <img src="assets/images/Finding joy in the simplicity of the sea ............beach bridge ocean smile sunny monument collage sunset sunrise travelphotography travel.jpg"
                    alt="Profile Image"
                    class="background-image">
                <img src="assets/images/Finding paradise wherever the waves take me. . . . . . . . . . . . . . . .beachbound beachlife beach beachdreaming ocean paradise wavesfordays explore rainyday shorelineadventures seasideescape beach.jpg"
                    alt="Profile Image Alternative"
                    class="background-image second-image">
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
            <div class="scroll-arrow" style="margin-top: -8px;"></div>
            <span class="scroll-text">Scroll Down</span>
        </div>
    </div>

    <!-- Updated section structure with proper containers -->
    <section id="about">
        <div class="section-container">
            <h2>About Me</h2>
            <div class="about-content">
                <div class="about-text">
                    <p class="about-intro">Recent Computer Science and Engineering graduate specializing in no-code/low-code development and AI-assisted tools. I focus on creating efficient digital solutions with minimal traditional coding, leveraging AI tools, cloud backends, and automation workflows. I'm seeking opportunities where I can apply my skills in AI prompt engineering, SaaS development, and mobile app creation to deliver cost-effective and innovative solutions.</p>

                    <div class="education-container">
                        <h3>Education</h3>
                        <div class="education-item">
                            <div class="education-year">Expected 2026</div>
                            <div class="education-details">
                                <h4>Bachelor of Engineering in Computer Science and Engineering</h4>
                                <p>NBKRIST College Autonomous</p>
                                <p>CGPA: 8.2 (Current)</p>
                            </div>
                        </div>
                        <div class="education-item">
                            <div class="education-year">Graduated 2022</div>
                            <div class="education-details">
                                <h4>Intermediate</h4>
                                <p>Narayana Junior College, State Board</p>
                                <p>CGPA: 5.55</p>
                            </div>
                        </div>
                        <div class="education-item">
                            <div class="education-year">Graduated 2020</div>
                            <div class="education-details">
                                <h4>SSC</h4>
                                <p>Narayana EM High School, State Board</p>
                                <p>CGPA: 9.88</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="skills-container">
                    <h3>Skills</h3>
                    <div class="skills-grid">
                        <div class="skill-category">
                            <h4>No-Code/Low-Code</h4>
                            <ul class="skills-list">
                                <li>SaaS development using AI-assisted tools & platforms</li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>Cloud & Backend</h4>
                            <ul class="skills-list">
                                <li>Supabase & Firebase – auth, DB, storage</li>
                                <li>API Integration & key management</li>
                                <li>Cost-optimized usage of 3rd-party services</li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>Mobile Development</h4>
                            <ul class="skills-list">
                                <li>Android & iOS dev via AI tools</li>
                                <li>Android Studio</li>
                                <li>Xcode</li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>AI & Automation</h4>
                            <ul class="skills-list">
                                <li>AI Prompt Engineering with low-iteration design</li>
                                <li>Workflow automation using n8n</li>
                                <li>Telegram bots for info delivery & engagement</li>
                            </ul>
                        </div>
                        <div class="skill-category">
                            <h4>Web Development</h4>
                            <ul class="skills-list">
                                <li>HTML</li>
                                <li>CSS</li>
                                <li>JavaScript</li>
                                <li>Basic front-end tasks</li>
                            </ul>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </section>

    <section id="projects">
        <div class="section-container">
            <h2>Projects</h2>
            <div class="projects-content">
                <!-- macOS Desktop Interface -->
                <div class="macos-desktop">
                    <!-- macOS Menu Bar -->
                    <div class="macos-menu-bar">
                        <div class="apple-logo"><i class="fab fa-apple"></i></div>
                        <div class="menu-items">
                            <span>Finder</span>
                            <span>File</span>
                            <span>Edit</span>
                            <span>View</span>
                            <span>Go</span>
                            <span>Window</span>
                            <span>Help</span>
                        </div>
                        <div class="menu-right">
                            <span><i class="fas fa-wifi"></i></span>
                            <span><i class="fas fa-battery-three-quarters"></i></span>
                            <span id="macos-clock">12:00 PM</span>
                        </div>
                    </div>

                    <!-- Desktop Content with Folders -->
                    <div class="macos-desktop-content">
                        <!-- Project 1 Folder -->
                        <div class="macos-folder" data-project="trade-book-ledge">
                            <div class="macos-folder-icon"></div>
                            <div class="macos-folder-name">Trade Book Ledge</div>
                        </div>

                        <!-- Project 2 Folder -->
                        <div class="macos-folder" data-project="nbkrist-portal">
                            <div class="macos-folder-icon"></div>
                            <div class="macos-folder-name">NBKRIST Portal</div>
                        </div>

                        <!-- More Projects Folder -->
                        <div class="macos-folder" data-project="more-projects">
                            <div class="macos-folder-icon"></div>
                            <div class="macos-folder-name">More Projects</div>
                        </div>
                    </div>

                    <!-- macOS Dock -->
                    <div class="macos-dock">
                        <div class="dock-item" data-tooltip="Safari">
                            <i class="fab fa-safari"></i>
                            <span class="dock-tooltip">Safari</span>
                        </div>
                        <div class="dock-item" data-tooltip="Mail">
                            <i class="fas fa-envelope"></i>
                            <span class="dock-tooltip">Mail</span>
                        </div>
                        <div class="dock-item" data-tooltip="Music">
                            <i class="fas fa-music"></i>
                            <span class="dock-tooltip">Music</span>
                        </div>
                        <div class="dock-item" data-tooltip="Calculator">
                            <i class="fas fa-calculator"></i>
                            <span class="dock-tooltip">Calculator</span>
                        </div>
                        <div class="dock-item" data-tooltip="Terminal">
                            <i class="fas fa-terminal"></i>
                            <span class="dock-tooltip">Terminal</span>
                        </div>
                        <div class="dock-item" data-tooltip="GitHub">
                            <i class="fab fa-github"></i>
                            <span class="dock-tooltip">GitHub</span>
                        </div>
                        <div class="dock-item" data-tooltip="LinkedIn">
                            <i class="fab fa-linkedin"></i>
                            <span class="dock-tooltip">LinkedIn</span>
                        </div>
                        <div class="dock-item" data-tooltip="Finder">
                            <i class="fas fa-folder"></i>
                            <span class="dock-tooltip">Finder</span>
                        </div>
                    </div>
                </div>

                <!-- Project Windows (Hidden by default) -->
                <div class="macos-overlay"></div>

                <!-- Trade Book Ledge Project Window -->
                <div class="macos-window" id="trade-book-ledge-window">
                    <div class="window-titlebar">
                        <div class="window-controls">
                            <div class="window-button window-close"></div>
                            <div class="window-button window-minimize"></div>
                            <div class="window-button window-maximize"></div>
                        </div>
                        <div class="window-title">Trade Book Ledge</div>
                    </div>
                    <div class="window-content">
                        <div class="project-window-content">
                            <div class="project-header">
                                <div class="project-icon"><i class="fas fa-laptop-code"></i></div>
                                <h3 class="project-window-title">Trade Book Ledge</h3>
                            </div>

                            <p class="project-window-description">A SaaS (Software as a Service) product for business management, targeting middlemen traders with subscription-based access model.</p>

                            <div class="project-files">
                                <div class="project-file">
                                    <div class="file-icon"><i class="fas fa-file-code"></i></div>
                                    <div class="file-name">index.html</div>
                                </div>
                                <div class="project-file">
                                    <div class="file-icon"><i class="fab fa-react"></i></div>
                                    <div class="file-name">App.tsx</div>
                                </div>
                                <div class="project-file">
                                    <div class="file-icon"><i class="fas fa-database"></i></div>
                                    <div class="file-name">schema.sql</div>
                                </div>
                                <div class="project-file">
                                    <div class="file-icon"><i class="fas fa-file-alt"></i></div>
                                    <div class="file-name">README.md</div>
                                </div>
                                <div class="project-file">
                                    <div class="file-icon"><i class="fas fa-cogs"></i></div>
                                    <div class="file-name">config.json</div>
                                </div>
                            </div>

                            <ul class="project-features">
                                <li>Leveraged no-code platforms and AI tools (ChatGPT, Claude) to rapidly build a scalable cloud solution</li>
                                <li>Created a complete SaaS ecosystem with web application (Vite, React, TypeScript)</li>
                                <li>Integrated with Supabase for secure user authentication and data management</li>
                            </ul>

                            <div class="project-window-footer">
                                <div class="project-tech">
                                    <span class="tech-tag">React</span>
                                    <span class="tech-tag">TypeScript</span>
                                    <span class="tech-tag">Vite</span>
                                    <span class="tech-tag">Supabase</span>
                                </div>

                                <div class="project-links">
                                    <a href="https://github.com/nrenx/Trade-Book-Ledge" target="_blank" class="project-link github"><i class="fab fa-github"></i> GitHub</a>
                                    <a href="https://nrenx.github.io/Trade-Book-Ledge/" target="_blank" class="project-link demo"><i class="fas fa-external-link-alt"></i> Live Demo</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- NBKRIST Student Portal Project Window -->
                <div class="macos-window" id="nbkrist-portal-window">
                    <div class="window-titlebar">
                        <div class="window-controls">
                            <div class="window-button window-close"></div>
                            <div class="window-button window-minimize"></div>
                            <div class="window-button window-maximize"></div>
                        </div>
                        <div class="window-title">NBKRIST Student Portal</div>
                    </div>
                    <div class="window-content">
                        <div class="project-window-content">
                            <div class="project-header">
                                <div class="project-icon"><i class="fas fa-university"></i></div>
                                <h3 class="project-window-title">NBKRIST Student Portal</h3>
                            </div>

                            <p class="project-window-description">A student portal featuring academic information access, attendance tracking, and exam results.</p>

                            <div class="project-files">
                                <div class="project-file">
                                    <div class="file-icon"><i class="fas fa-file-code"></i></div>
                                    <div class="file-name">index.html</div>
                                </div>
                                <div class="project-file">
                                    <div class="file-icon"><i class="fab fa-css3"></i></div>
                                    <div class="file-name">styles.css</div>
                                </div>
                                <div class="project-file">
                                    <div class="file-icon"><i class="fab fa-js"></i></div>
                                    <div class="file-name">main.js</div>
                                </div>
                                <div class="project-file">
                                    <div class="file-icon"><i class="fas fa-table"></i></div>
                                    <div class="file-name">attendance.js</div>
                                </div>
                                <div class="project-file">
                                    <div class="file-icon"><i class="fas fa-graduation-cap"></i></div>
                                    <div class="file-name">results.js</div>
                                </div>
                            </div>

                            <ul class="project-features">
                                <li>Developed using AI tools and no-code approaches</li>
                                <li>Integrated modern technologies (Vite, React, Tailwind CSS) and Supabase backend</li>
                                <li>Created responsive design and automated deployment to GitHub Pages</li>
                            </ul>

                            <div class="project-window-footer">
                                <div class="project-tech">
                                    <span class="tech-tag">React</span>
                                    <span class="tech-tag">Tailwind CSS</span>
                                    <span class="tech-tag">Supabase</span>
                                    <span class="tech-tag">GitHub Pages</span>
                                </div>

                                <div class="project-links">
                                    <a href="https://github.com/nrenx/nbkrist-student-portal.git" target="_blank" class="project-link github"><i class="fab fa-github"></i> GitHub</a>
                                    <a href="https://nbkrstudenthub.me/" target="_blank" class="project-link demo"><i class="fas fa-external-link-alt"></i> Live Demo</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- More Projects Window -->
                <div class="macos-window" id="more-projects-window">
                    <div class="window-titlebar">
                        <div class="window-controls">
                            <div class="window-button window-close"></div>
                            <div class="window-button window-minimize"></div>
                            <div class="window-button window-maximize"></div>
                        </div>
                        <div class="window-title">More Projects</div>
                    </div>
                    <div class="window-content">
                        <div class="project-window-content">
                            <div class="project-header">
                                <div class="project-icon"><i class="fas fa-folder-open"></i></div>
                                <h3 class="project-window-title">More Projects</h3>
                            </div>

                            <p class="project-window-description">Check out my GitHub profile for more projects and code samples.</p>

                            <div class="project-window-footer">
                                <div class="project-links">
                                    <a href="https://github.com/nrenx" target="_blank" class="project-link github">
                                        <i class="fab fa-github"></i> Visit GitHub Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="experience">
        <div class="section-container">
            <h2>Experience & Certifications</h2>
            <div class="experience-content">
                <!-- Certifications -->
                <div class="certifications-container">
                    <h3>Certifications</h3>
                    <div class="certifications-grid">
                        <div class="certification-card">
                            <div class="certification-icon">
                                <i class="fas fa-certificate"></i>
                            </div>
                            <div class="certification-details">
                                <h4>Critical Thinking & Problem Solving</h4>
                                <p>LinkedIn Learning (2023)</p>
                            </div>
                        </div>

                        <div class="certification-card">
                            <div class="certification-icon">
                                <i class="fab fa-python"></i>
                            </div>
                            <div class="certification-details">
                                <h4>Python Programming</h4>
                                <p>Coursera (2022)</p>
                            </div>
                        </div>

                        <div class="certification-card">
                            <div class="certification-icon">
                                <i class="fas fa-code"></i>
                            </div>
                            <div class="certification-details">
                                <h4>Web Development Fundamentals</h4>
                                <p>Udemy (2023)</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Extracurricular Activities -->
                <div class="activities-container">
                    <h3>Extracurricular Activities</h3>
                    <ul class="activities-list">
                        <li>
                            <i class="fas fa-laptop-code"></i>
                            <span>Participated in college-level coding competitions (2022-2023)</span>
                        </li>
                        <li>
                            <i class="fas fa-users"></i>
                            <span>Member of the Computer Science Club at NBKRIST College</span>
                        </li>
                        <li>
                            <i class="fas fa-hands-helping"></i>
                            <span>Volunteer for technical events at department symposiums</span>
                        </li>
                    </ul>
                </div>


            </div>
        </div>
    </section>

    <section id="contact">
        <div class="section-container">
            <div class="contact-content">
                <div class="contact-top">
                    <h1>GOT A VISION? LET'S BRING IT TO LIFE!</h1>
                    <p>I'm enthusiastic about collaborating on innovative projects. Let's connect and explore how we can bring your vision to life!</p>
                </div>

                <div class="contact-form-container">
                    <div class="contact-info">
                        <h3>Contact Information</h3>
                        <div class="contact-info-item">
                            <i class="fas fa-envelope"></i>
                            <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>
                        </div>
                        <div class="contact-info-item">
                            <i class="fas fa-phone"></i>
                            <a href="tel:+917989976214" class="phone-link">+91 ************</a>
                        </div>
                        <div class="contact-info-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>Edulapalli(Vi), Gudur(M), Tirupathi(D), Andhra Pradesh, 524409</span>
                        </div>

                        <div class="social-icons">
                            <a href="https://linkedin.com/in/bollineninarendrachowdary" class="social-icon linkedin" target="_blank" rel="noopener noreferrer"><i class="fab fa-linkedin"></i></a>
                            <a href="https://github.com/nrenx" class="social-icon github" target="_blank" rel="noopener noreferrer"><i class="fab fa-github"></i></a>
                            <a href="#" class="social-icon twitter" target="_blank" rel="noopener noreferrer"><i class="fab fa-twitter"></i></a>
                        </div>

                        <button class="resume-button">
                            <i class="fas fa-file-pdf"></i> View Resume
                        </button>
                    </div>

                    <div class="contact-form">
                        <h3>Send Me a Message</h3>
                        <form id="contact-form">
                            <div class="form-group">
                                <label for="name">Name</label>
                                <input type="text" id="name" name="name" placeholder="Your Name" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email" placeholder="Your Email" required>
                            </div>
                            <div class="form-group">
                                <label for="subject">Subject</label>
                                <input type="text" id="subject" name="subject" placeholder="Subject" required>
                            </div>
                            <div class="form-group">
                                <label for="message">Message</label>
                                <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                            </div>
                            <button type="submit" class="submit-button">
                                <span>Send Message</span>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>

                <div class="contact-footer">
                    <p>© 2024 Bollineni Narendra Chowdary. All rights reserved.</p>
                </div>
            </div>
        </div>
    </section>

    <script src="javascript.js"></script>
</body>
</html>
