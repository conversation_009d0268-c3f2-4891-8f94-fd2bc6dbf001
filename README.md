# Narendra Chowdary Portfolio

A modern, interactive portfolio website showcasing my skills, projects, and experience as a No-Code Developer, AI Prompt Engineer, and Mobile App Creator.

## 🌟 Features

- **Interactive macOS Interface** - Project section features a realistic macOS desktop experience with interactive folders, windows, and dock
- **Responsive Design** - Fully responsive layout that works seamlessly on desktop, tablet, and mobile devices
- **Dark/Light Mode Toggle** - Custom-built theme switcher with smooth transitions and particle effects
- **Animated Sections** - Smooth scroll animations and section transitions enhance user experience
- **Professional Contact Form** - Clean, accessible contact form for potential clients and employers
- **Resume Integration** - Direct access to view and download my professional resume

## 💻 Technologies Used

- **HTML5** - Semantic markup for improved accessibility and SEO
- **CSS3** - Custom styling with modern CSS features
- **JavaScript** - Interactive elements and animations
- **Font Awesome** - Icon library for visual elements
- **Google Fonts** - Typography enhancement with Inter and Poppins fonts

## 🏗️ Project Structure

```
portfolio/
├── assets/
│   ├── images/ - Portfolio images
│   └── resume/ - Resume PDF
├── css/
│   ├── about.css - About section styles
│   ├── contact.css - Contact section styles
│   ├── experience.css - Experience section styles
│   ├── home.css - Home section styles
│   ├── macos-interface.css - macOS interface styles
│   ├── projects.css - Projects section styles
│   └── sections.css - General section styles
├── style.css - Main stylesheet
├── javascript.js - Main JavaScript file
└── index.html - Main HTML document
```

## 🚀 Key Features

### macOS Desktop Interface
- Interactive folders that open project windows
- Realistic window controls (close, minimize, maximize)
- Draggable windows with proper animations
- Dock with hover effects and tooltips
- Authentic macOS styling and behavior

### Responsive Navigation
- Modern navbar with smooth section transitions
- Mobile-optimized menu with animations
- Active section highlighting
- Scroll-aware navigation updates

### Dark/Light Mode
- Custom-built theme toggle with light bulb animation
- Particle animation background in dark mode
- Persistent theme preference
- Smooth transition between themes

### Animated Landing Screen
- Multi-language greeting animation
- Particle effects during transition
- Smooth reveal of main content

## 🛠️ Setup and Usage

1. Clone the repository
   ```
   git clone https://github.com/nrenx/portfolio.git
   ```

2. Open the project folder
   ```
   cd portfolio
   ```

3. Open `index.html` in your browser or use a local server
   ```
   # Using Python's built-in server
   python -m http.server
   ```

4. For development, you can modify the CSS files in the `css/` directory and the main JavaScript file `javascript.js`

## 📱 Mobile Optimization

- Responsive design adapts to all screen sizes
- Touch-friendly navigation and interactive elements
- Optimized performance for mobile devices
- Adjusted animations for better mobile experience

## 🔗 Links

- [GitHub](https://github.com/nrenx)
- [LinkedIn](https://linkedin.com/in/bollineninarendrachowdary)
- [Live Demo](https://your-portfolio-url.com)

## 📄 License

This project is available for personal use and as a portfolio reference. Please contact me for any other usage.

## 📞 Contact

Feel free to reach out to <NAME_EMAIL> for any questions or opportunities.