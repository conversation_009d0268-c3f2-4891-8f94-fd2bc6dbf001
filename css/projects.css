/* Projects section specific styles */
#projects {
  background-color: hsl(var(--background));
  position: relative;
  overflow: hidden;
}

#projects .projects-content {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

#projects .projects-content.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Project grid layout */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

/* Project card styles */
.project-card {
  border-radius: 0.75rem;
  overflow: hidden;
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  height: 100%;
  display: flex;
  flex-direction: row;
  margin-bottom: 2rem;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  border-color: hsla(var(--primary), 0.3);
}

.project-content {
  padding: 1.8rem;
  flex: 2;
  display: flex;
  flex-direction: column;
}

.project-image-container {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: hsla(var(--muted), 0.3);
  position: relative;
  overflow: hidden;
}

.project-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: hsl(var(--primary));
  font-size: 4rem;
  transition: all 0.3s ease;
}

.project-card:hover .project-image-placeholder {
  transform: scale(1.1) rotate(5deg);
  color: hsl(var(--accent-foreground));
}

.project-image-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, hsla(var(--primary), 0.05) 0%, transparent 100%);
  z-index: 1;
}

.project-features {
  margin: 1rem 0;
  padding-left: 1.2rem;
  list-style-type: none;
}

.project-features li {
  position: relative;
  margin-bottom: 0.8rem;
  line-height: 1.5;
  color: hsl(var(--muted-foreground));
  font-size: 0.9rem;
}

.project-features li::before {
  content: '→';
  position: absolute;
  left: -1.2rem;
  color: hsl(var(--primary));
  font-weight: bold;
}

.project-links {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.project-link.github {
  background-color: hsla(var(--muted), 0.5);
  color: hsl(var(--foreground));
}

.project-link.demo {
  background-color: hsla(var(--primary), 0.1);
  color: hsl(var(--primary));
}

.project-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.project-link.github:hover {
  background-color: #24292e;
  color: white;
}

.project-link.demo:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.project-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.project-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: hsl(var(--foreground));
}

.project-description {
  color: hsl(var(--muted-foreground));
  font-size: 0.875rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  flex-grow: 1;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: auto;
}

.tech-tag {
  background-color: hsl(var(--muted));
  color: hsl(var(--muted-foreground));
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.project-card:hover .tech-tag {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

/* Project filter */
.project-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 2rem;
  justify-content: center;
}

.filter-button {
  background-color: transparent;
  border: 1px solid hsl(var(--border));
  color: hsl(var(--foreground));
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.filter-button:hover, .filter-button.active {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border-color: hsl(var(--primary));
}

/* Loading state */
.project-card.loading .project-image {
  background: linear-gradient(90deg, hsl(var(--muted)) 0%, hsl(var(--card)) 50%, hsl(var(--muted)) 100%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.project-card.loading .project-title,
.project-card.loading .project-description,
.project-card.loading .tech-tag {
  background: linear-gradient(90deg, hsl(var(--muted)) 0%, hsl(var(--card)) 50%, hsl(var(--muted)) 100%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  color: transparent;
  border-radius: 0.25rem;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* More Projects Link */
.more-projects {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  margin-bottom: 2rem;
}

.more-projects-link {
  display: inline-flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.8rem 1.5rem;
  background-color: hsla(var(--primary), 0.1);
  color: hsl(var(--primary));
  border-radius: 2rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.more-projects-link:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.more-projects-link i {
  font-size: 1.2rem;
}

/* Mobile styles */
@media (max-width: 992px) {
  .project-card {
    flex-direction: column;
  }

  .project-image-container {
    min-height: 200px;
  }
}

@media (max-width: 768px) {
  .projects-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .project-filters {
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .filter-button {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .project-card {
    margin-bottom: 1.5rem;
  }

  .project-features li {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .project-image-container {
    min-height: 180px;
  }

  .project-content {
    padding: 1.2rem;
  }

  .project-title {
    font-size: 1.1rem;
  }

  .project-links {
    flex-direction: column;
    gap: 0.75rem;
  }

  .project-link {
    width: 100%;
    justify-content: center;
  }
}