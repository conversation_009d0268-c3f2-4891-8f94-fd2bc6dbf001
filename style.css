/* Base styles */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variables for colors */
:root {
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 240 5.9% 10%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 4.8% 95.9%;
  --secondary-foreground: 240 5.9% 10%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 5.9% 10%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 5.9% 10%;
  --radius: 0.5rem;
  --primary-color: #f5f3f1;
  --secondary-color: #ddd;
  --text-color: #000;
  --dark-theme-background: #333;
  --dark-theme-header: #555;
}

/* Dark mode variables */
.dark, body.dark-theme {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 240 5.9% 10%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 4.9% 83.9%;
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
  font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  padding-top: 80px; /* Space for fixed navbar */
  transition: background-color 0.3s ease, color 0.3s ease;
  line-height: 1.6;
}

/* Navbar Styles */
.navbar-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 40;
  transition: all 0.3s ease;
  background-color: transparent;
}

.navbar-header.scrolled {
  background-color: hsla(var(--background), 0.5);
  backdrop-filter: blur(8px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-container {
  display: flex;
  height: 60px; /* HEIGHT OF NAV BAR Increased from 49px to 60px for a taller navbar */
  max-width: 1200px;
  margin: 0 auto;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.navbar-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

.logo-svg {
  height: 32px;
  width: 32px;
  margin-right: 8px;
  stroke: #000;
}

body.dark-theme .logo-svg {
  stroke: #fff;
}

.navbar-name {
  font-size: 18px;
  font-weight: bold;
  color: hsl(var(--foreground));
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
  align-items: center;
  gap: 24px;
  animation: slideDown 0.5s ease;
}

@keyframes slideDown {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.nav-link {
  position: relative;
  padding: 8px 4px;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
  background: none;
  border: none;
  cursor: pointer;
  color: hsl(var(--muted-foreground));
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: hsl(var(--foreground));
}

.nav-link.active {
  color: hsl(var(--primary));
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px; /* Increased from 1px to 3px for a more visible indicator */
  background-color: hsl(var(--primary));
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { opacity: 0; width: 0; }
  to { opacity: 1; width: 100%; }
}

/* Navbar Actions */
.navbar-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Mobile menu button */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: hsl(var(--muted-foreground));
  transition: background-color 0.3s ease, color 0.3s ease;
}

.mobile-menu-btn:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--foreground));
}

.menu-icon, .close-icon {
  font-size: 20px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.hidden {
  opacity: 0;
  visibility: hidden;
  transform: translate(-50%, -50%) scale(0.5);
  pointer-events: none;
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  inset: 60px 0 auto 0;
  z-index: 30;
  background-color: hsla(var(--background), 0.95);
  backdrop-filter: blur(10px);
  transform: translateY(-100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  padding: 8px 0;
  display: none;
  max-height: calc(100vh - 60px);
  overflow-y: auto;
}

.mobile-nav.open {
  transform: translateY(0);
  opacity: 1;
}

.mobile-nav nav {
  display: flex;
  flex-direction: column;
}

.mobile-nav .nav-link {
  padding: 14px 20px;
  font-size: 16px;
  text-align: left;
  position: relative;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  letter-spacing: 0.5px;
}

.mobile-nav .nav-link::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: transparent;
  transition: background-color 0.2s ease;
}

.mobile-nav .nav-link.active::after {
  background-color: hsl(var(--primary));
}

/* Switch styles - Light/Dark toggle */
.switch {
  position: relative;
}

.switch input {
  height: 100%;
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
  z-index: 100;
  cursor: pointer;
}

.switch label {
  height: 60px;
  width: 60px;
  background-color: #ffffff;
  border-radius: 100px;
  display: block;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.2), inset 0 0 5px -2px rgba(0, 0, 0, 0.4);
}

/* Bulb styles */
.bulb {
  height: 50px;
  width: 50px;
  background-color: #2d2e32;
  border-radius: 50%;
  position: relative;
  top: 5px;
  left: 5px;
  display: block;
  transition: 0.9s;
  box-shadow: inset 0 0 1px 3px #2d2e32, inset 0 0 6px 8px #1e1e20, 0 20px 30px -10px rgba(0, 0, 0, 0.2);
}

.bulb-center {
  position: absolute;
  display: block;
  height: 36px;
  width: 36px;
  background-color: #3a3a3c;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transition: 0.7s;
  transform: translate(-50%, -50%);
  box-shadow: inset 0 0 0 4px #444;
}

.bulb-center:after {
  content: "";
  display: block;
  height: 14px;
  width: 14px;
  background-color: #4a4a4c;
  border-radius: 50%;
  position: absolute;
  transition: 0.7s;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 2px 4px #2a2a2c;
}

/* Filament styles */
.filament-1,
.filament-2 {
  position: absolute;
  display: block;
  height: 35px;
  width: 35px;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  overflow: hidden;
  transform: translate(-50%, -50%) rotate(-45deg);
}

.filament-1:after,
.filament-1:before,
.filament-2:after,
.filament-2:before {
  content: "";
  display: block;
  height: 6px;
  width: 17px;
  border-radius: 50%;
  border: 2px solid #4a426b;
  position: absolute;
  transition: 0.7s;
  top: -4px;
  left: -2px;
  transform: rotate(-10deg);
}

.filament-1:before,
.filament-2:before {
  left: 15px;
  transform: rotate(10deg);
}

.filament-2 {
  transform: translate(-50%, -50%) rotate(45deg) !important;
}

/* Toggle ON state styles */
.switch input:checked ~ label .bulb {
  background-color: #a7694a;
  box-shadow: inset 0 0 1px 3px #a56758, inset 0 0 6px 8px #6b454f, 0 20px 30px -10px rgba(0, 0, 0, 0.4), 0 0 30px 50px rgba(253, 184, 67, 0.1);
}

.switch input:checked ~ label .bulb .bulb-center {
  background-color: #feed6b;
  box-shadow: inset 0 0 0 4px #fdec6a, 0 0 12px 10px #bca83c, 0 0 20px 14px #a1664a;
}

.switch input:checked ~ label .bulb .bulb-center:after {
  background-color: #fef401;
  box-shadow: 0 0 2px 4px #fdb843;
}

.switch input:checked ~ label .bulb .filament-1:before,
.switch input:checked ~ label .bulb .filament-1:after,
.switch input:checked ~ label .bulb .filament-2:before,
.switch input:checked ~ label .filament-2:after {
  border-color: #fef4d5;
}

/* Sparks Effect styles */
.sparks i {
  display: block;
  height: 3px;
  width: 3px;
  background-color: #d1b82b;
  position: absolute;
  border-radius: 50%;
  opacity: 0;
  transition: 0.4s;
}

.spark1 { right: -5px; bottom: 23px; }
.spark2 { right: 20px; bottom: 80px; }
.spark3 { left: 20px; bottom: 80px; }
.spark4 { left: 20px; bottom: 20px; }
.spark5 { right: 10px; top: 10px; }
.spark6 { left: 10px; top: 10px; }
.spark7 { right: 30px; bottom: 10px; }
.spark8 { left: 30px; bottom: 10px; }

.switch input:checked ~ label .bulb .sparks .spark1 {
  animation: spark1 2s ease-in-out 0.4s infinite;
}

.switch input:checked ~ label .bulb .sparks .spark2 {
  animation: spark2 2.4s ease-in-out 0.4s infinite;
}

.switch input:checked ~ label .bulb .sparks .spark3 {
  animation: spark3 2s ease-in-out 0.9s infinite;
}

.switch input:checked ~ label .bulb .sparks .spark4 {
  animation: spark4 1.7s ease-in-out 0.9s infinite;
}

.switch input:checked ~ label .bulb .sparks .spark5 {
  animation: spark5 1.5s ease-in-out 0.5s infinite;
}

.switch input:checked ~ label .bulb .sparks .spark6 {
  animation: spark6 2.2s ease-in-out 0.7s infinite;
}

.switch input:checked ~ label .bulb .sparks .spark7 {
  animation: spark7 1.9s ease-in-out 0.6s infinite;
}

.switch input:checked ~ label .bulb .sparks .spark8 {
  animation: spark8 2.1s ease-in-out 0.8s infinite;
}

/* Spark animations */
@keyframes spark1 {
  0% { opacity: 0; right: -5px; }
  20% { opacity: 1; right: 0px; }
  100% { opacity: 0; right: -70px; bottom: 50px; }
}

@keyframes spark2 {
  0% { opacity: 0; }
  30% { opacity: 1; }
  100% { opacity: 0; right: 10px; bottom: 90px; }
}

@keyframes spark3 {
  0% { opacity: 0; }
  30% { opacity: 1; }
  100% { opacity: 0; left: -30px; bottom: 60px; }
}

@keyframes spark4 {
  0% { opacity: 0; }
  30% { opacity: 1; }
  100% { opacity: 0; left: 50px; bottom: 30px; }
}

@keyframes spark5 {
  0% { opacity: 0; right: 10px; top: 10px; }
  30% { opacity: 1; }
  100% { opacity: 0; right: 60px; top: 50px; }
}

@keyframes spark6 {
  0% { opacity: 0; left: 10px; top: 10px; }
  30% { opacity: 1; }
  100% { opacity: 0; left: 40px; top: 70px; }
}

@keyframes spark7 {
  0% { opacity: 0; right: 30px; bottom: 10px; }
  30% { opacity: 1; }
  100% { opacity: 0; right: 80px; bottom: -10px; }
}

@keyframes spark8 {
  0% { opacity: 0; left: 30px; bottom: 10px; }
  30% { opacity: 1; }
  100% { opacity: 0; left: 70px; bottom: 0px; }
}

/* Dark theme styles */
body.dark-theme {
  background-color: var(--dark-theme-background);
  color: #fff;
}

body.dark-theme .navbar-header:not(.scrolled) {
  background-color: transparent;
  border: none;
  box-shadow: none;
}

body.dark-theme .navbar-header.scrolled {
  background-color: hsla(var(--background), 0.8);
  backdrop-filter: blur(8px);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5); /* Darker shadow for dark mode */
}

body.dark-theme .switch label {
  background-color: var(--dark-theme-background);
}

/* Cursor dot styles */
.cursor-dot {
  width: 12px;
  height: 12px;
  background-color: black;
  border-radius: 50%;
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: none;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease-out;
  opacity: 0.8;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

body.dark-theme .cursor-dot {
  background-color: white;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

/* Dark mode animation styles */
.dark-mode-animation {
  transition: background-color 1s ease;
  position: relative;
  overflow: hidden;
}

.dark-mode-animation::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100%);
  transform: scale(0);
  transform-origin: top right;
  transition: transform 1s ease;
  z-index: 10;
}

body.dark-theme.dark-mode-animation::before {
  transform: scale(2);
}

/* Landing Page Styles */
.landing-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #000, #222);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  transition: transform 1.2s cubic-bezier(0.6, 0.1, 0.3, 1), opacity 1s ease-in-out;
  z-index: 2000;
  overflow: hidden;
  perspective: 1000px; /* Add 3D perspective */
}

/* Background effect for landing page */
.background-effect {
  position: absolute;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(0,0,0,0) 70%);
  animation: bg-pan 5s linear infinite;
}

@keyframes bg-pan {
  from { transform: translate(-25%, -25%); }
  to { transform: translate(-75%, -75%); }
}

/* Improved hidden state with 3D effect */
.landing-screen.hidden {
  transform: translateY(-100%) rotateX(10deg);
  opacity: 0;
}

/* Particles for transition effect */
.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  pointer-events: none;
}

/* Message container for landing page */
.message-container {
  position: relative;
  height: 120px;
  width: 100%;
  text-align: center;
  transform-style: preserve-3d;
}

.message {
  position: absolute;
  width: 100%;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  transform-origin: center center;
  font-size: 3.2rem;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.95);
  opacity: 0;
  transition: opacity 0.3s ease-in-out, transform 0.5s ease-in-out;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.message.visible {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
}

/* Enhanced entrance for main content */
.reveal-content {
  animation: revealContent 1.2s cubic-bezier(0.6, 0.1, 0.3, 1) forwards;
}

@keyframes revealContent {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Back to top button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease, background-color 0.3s ease;
  z-index: 30;
}

.back-to-top.visible {
  opacity: 1;
  transform: translateY(0);
}

.back-to-top:hover {
  background-color: hsl(var(--accent));
  transform: translateY(-5px);
}

/* Scroll progress indicator */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent)));
  z-index: 50;
  transition: width 0.1s ease;
}

/* Image loading animation */
img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.5s ease;
}

img.loaded {
  opacity: 1;
}

/* Tooltip styles */
[data-tooltip] {
  position: relative;
  cursor: pointer;
}

[data-tooltip]:before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-5px);
  padding: 5px 10px;
  border-radius: 4px;
  background-color: hsl(var(--foreground));
  color: hsl(var(--background));
  font-size: 0.75rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease;
  z-index: 10;
}

[data-tooltip]:hover:before {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-10px);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .navbar-container {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {
  body {
    padding-top: 60px; /* Reduced for mobile */
  }

  .navbar-header {
    height: 60px; /* Fixed height for mobile navbar */
  }

  .navbar-container {
    height: 100%;
    padding: 0 16px;
    justify-content: space-between;
  }

  .desktop-nav {
    display: none;
  }

  .mobile-menu-btn {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: hsla(var(--muted), 0.2);
    transition: background-color 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .mobile-menu-btn:hover {
    background-color: hsla(var(--muted), 0.3);
  }

  .mobile-menu-btn i {
    font-size: 18px;
    color: hsl(var(--foreground));
  }

  .mobile-menu-btn .menu-icon.hidden,
  .mobile-menu-btn .close-icon.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translate(-50%, -50%) scale(0.5);
  }

  .mobile-nav {
    display: block;
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid hsla(var(--border), 0.5);
  }

  .mobile-nav .nav-link {
    font-size: 16px;
    padding: 14px 16px;
    border-bottom: 1px solid hsla(var(--border), 0.1);
    transition: background-color 0.3s ease;
  }

  .mobile-nav .nav-link:hover {
    background-color: hsla(var(--muted), 0.1);
  }

  .mobile-nav .nav-link.active {
    color: hsl(var(--primary));
    font-weight: 500;
    background-color: hsla(var(--primary), 0.05);
  }

  .switch label {
    height: 45px;
    width: 45px;
  }

  .bulb {
    height: 35px;
    width: 35px;
  }

  .logo-svg {
    height: 28px;
    width: 28px;
  }

  .navbar-name {
    font-size: 16px;
    font-weight: 500;
  }

  .navbar-logo {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  body {
    padding-top: 56px;
  }

  .navbar-header {
    height: 56px;
  }

  .navbar-container {
    padding: 0 12px;
  }

  .logo-svg {
    height: 24px;
    width: 24px;
  }

  .navbar-name {
    font-size: 15px;
    font-weight: 500;
  }

  .mobile-menu-btn {
    width: 36px;
    height: 36px;
  }

  .mobile-menu-btn i {
    font-size: 16px;
  }

  .switch label {
    height: 40px;
    width: 40px;
  }

  .bulb {
    height: 30px;
    width: 30px;
    top: 5px;
    left: 5px;
  }

  .mobile-nav {
    top: 56px;
  }

  .mobile-nav .nav-link {
    padding: 12px 16px;
    font-size: 15px;
  }
}
