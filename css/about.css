/* About section specific styles */
#about {
  background-color: hsl(var(--background));
  position: relative;
  overflow: hidden;
}

#about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 10% 20%, hsla(var(--primary), 0.03) 0%, transparent 60%);
  z-index: 0;
}

#about .about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  position: relative;
  z-index: 1;
}

#about .about-text {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

#about .about-intro {
  font-size: 1.1rem;
  line-height: 1.7;
  color: hsl(var(--foreground));
  border-left: 3px solid hsl(var(--primary));
  padding-left: 16px;
  margin-bottom: 20px;
}

#about .education-container {
  margin-top: 20px;
}

#about h3 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: hsl(var(--foreground));
  position: relative;
  display: inline-block;
}

#about h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: hsl(var(--primary));
  border-radius: 2px;
}

#about .education-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
  padding-left: 20px;
}

#about .education-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: hsl(var(--primary));
}

#about .education-year {
  min-width: 120px;
  font-weight: 600;
  color: hsl(var(--primary));
}

#about .education-details h4 {
  font-size: 1.1rem;
  margin-bottom: 5px;
  color: hsl(var(--foreground));
}

#about .education-details p {
  color: hsl(var(--muted-foreground));
  margin-bottom: 5px;
}

#about .skills-container {
  background-color: hsla(var(--card), 0.5);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid hsl(var(--border));
}

#about .skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 30px;
}

#about .skill-category h4 {
  font-size: 1.1rem;
  margin-bottom: 15px;
  color: hsl(var(--foreground));
  position: relative;
  display: inline-block;
}

#about .skill-category h4::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 30px;
  height: 2px;
  background-color: hsl(var(--primary));
}

#about .skills-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

#about .skills-list li {
  position: relative;
  padding: 8px 0 8px 25px;
  color: hsl(var(--foreground));
}

#about .skills-list li::before {
  content: '\f00c';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  left: 0;
  top: 8px;
  color: hsl(var(--primary));
  font-size: 0.8rem;
}

#about .languages-container {
  margin-top: 30px;
}

#about .languages-list {
  display: flex;
  gap: 20px;
  list-style: none;
  padding: 0;
}

#about .languages-list li {
  background-color: hsla(var(--primary), 0.1);
  color: hsl(var(--foreground));
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Dark mode specific styles */
.dark-theme #about .skills-container {
  background-color: hsla(var(--card), 0.2);
  border-color: hsla(var(--border), 0.3);
}

/* Animation for skills */
@keyframes skillFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

#about .skill-category {
  animation: skillFadeIn 0.5s ease forwards;
  opacity: 0;
}

#about .skill-category:nth-child(1) { animation-delay: 0.1s; }
#about .skill-category:nth-child(2) { animation-delay: 0.2s; }
#about .skill-category:nth-child(3) { animation-delay: 0.3s; }

/* Responsive styles */
@media (max-width: 992px) {
  #about .about-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  #about .skills-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  #about .about-content {
    padding: 0 10px;
  }

  #about .education-item {
    flex-direction: column;
    gap: 5px;
  }

  #about .skills-grid {
    grid-template-columns: 1fr 1fr;
  }

  #about .languages-list {
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  #about .skills-grid {
    grid-template-columns: 1fr;
  }

  #about .about-intro {
    font-size: 1rem;
  }

  #about h3 {
    font-size: 1.3rem;
  }
}
