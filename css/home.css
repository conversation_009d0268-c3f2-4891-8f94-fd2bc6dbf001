/* Introduction styles */
.introduction {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px 20px;
  min-height: calc(100vh - 80px); /* Adjust to account for navbar height */
  box-sizing: border-box; /* Include padding in height calculation */
  position: relative;
  overflow: hidden;
}

.introduction::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 30%, hsla(var(--primary), 0.03) 0%, transparent 70%);
  z-index: 0;
}

.image-container {
  width: 40%;
  margin-left: 20px;
  position: relative;
  z-index: 1;
}

.text-container {
  width: 55%;
  margin-right: 20px;
  padding-left: 130px; /* Left padding for main content */
  position: relative;
  z-index: 1;
}

.online {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #10b981;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 10px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
  position: relative;
}

.status-dot::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(16, 185, 129, 0.3);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
  70% { transform: translate(-50%, -50%) scale(1.5); opacity: 0; }
  100% { transform: translate(-50%, -50%) scale(1); opacity: 0; }
}

.intro-heading {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  margin-top: 10px;
  color: hsl(var(--foreground));
  line-height: 1.2;
}

.wave-emoji {
  display: inline-block;
  animation: wave 2.5s infinite;
  transform-origin: 70% 70%;
  font-size: 1em;
}

@keyframes wave {
  0% { transform: rotate(0deg); }
  10% { transform: rotate(14deg); }
  20% { transform: rotate(-8deg); }
  30% { transform: rotate(14deg); }
  40% { transform: rotate(-4deg); }
  50% { transform: rotate(10deg); }
  60% { transform: rotate(0deg); }
  100% { transform: rotate(0deg); }
}

.bio-container {
  margin: 20px 0;
}

.bio-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: hsl(var(--primary));
}

.bio-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: hsl(var(--muted-foreground));
  max-width: 600px;
}

/* Morph container styles */
.morph-container {
  position: relative;
  width: 300px;
  height: 300px;
  overflow: hidden;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  border: 4px solid #2d2e32;
  border-radius: 60% 40% 30% 70%/60% 30% 70% 40%;
  animation: morph 8s ease-in-out infinite;
  transition: all 1s ease-in-out;
  margin: 0 auto;
}

.morph-container img {
  width: 100%;
  height: auto;
  transition: opacity 1s ease-in-out;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  transition: opacity 0.5s ease-in-out;
}

.second-image {
  opacity: 0;
  z-index: 2;
  transition: opacity 0.5s ease-in-out;
}

.morph-container:hover .background-image {
  opacity: 0;
}

.morph-container:hover .second-image {
  opacity: 1;
}

@keyframes morph {
  0%,
  100% {
    border-radius: 60% 40% 30% 70%/60% 30% 70% 40%;
  }

  25% {
    border-radius: 40% 60% 70% 30%/40% 70% 30% 60%;
  }

  50% {
    border-radius: 30% 60% 70% 40%/50% 60% 30% 40%;
  }

  75% {
    border-radius: 55% 45% 40% 60%/45% 30% 60% 55%;
  }
}

/* Button Styles */
.button-container {
  display: flex;
  gap: 20px; /* Reduced for better alignment */
  margin-top: 30px;
  margin-left: 0; /* Reset negative margin */
  padding-left: 100px; /* Reduced padding to move buttons more to the left */
}

.button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 14px 28px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.button:hover::before {
  left: 100%;
}

.button:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.get-in-touch {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.view-projects {
  background-color: transparent;
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
}

.button i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.button:hover i {
  transform: translateX(5px);
}

/* Social Icons Styles */
.social-icons {
  display: flex;
  gap: 30px; /* Increased gap between icons for better spacing */
  margin-top: 60px; /* Further increased margin to move icons down */
  margin-left: 0; /* Align with the main content */
  padding-left: 100px; /* Reduced padding to match the button container */
  width: 55%; /* Match with text-container width */
  justify-content: flex-start; /* Align to the left of the container */
  padding-right: 20px; /* Add some right padding */
  transform: translateX(0); /* No horizontal offset for better alignment with main content */
  position: relative; /* Add position relative for potential absolute positioning */
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px; /* Increased size for better visibility */
  height: 45px; /* Increased size for better visibility */
  background-color: hsla(var(--muted), 0.2); /* Lighter background for minimalistic look */
  color: hsl(var(--foreground));
  border-radius: 50%;
  font-size: 1.4rem; /* Increased icon size */
  transition: all 0.3s ease;
  text-decoration: none;
  margin-right: 0; /* Remove right margin as we're using gap in the parent */
  border: 1px solid transparent; /* Transparent border that will show on hover */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* Subtle shadow for depth */
}

.social-icon:hover {
  transform: translateY(-5px);
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
  border-color: hsl(var(--primary)); /* Show border on hover */
}

/* Specific styles for different social icons */
.social-icon.linkedin {
  background-color: rgba(10, 102, 194, 0.1);
  color: #0A66C2;
}

.social-icon.github {
  background-color: rgba(36, 41, 46, 0.1);
  color: #24292E;
}

.social-icon.twitter {
  background-color: rgba(29, 161, 242, 0.1);
  color: #1DA1F2;
}

/* Dark mode specific styles for social icons */
body.dark-theme .social-icon {
  background-color: rgba(255, 255, 255, 0.1);
  color: #f5f5f7;
}

body.dark-theme .social-icon.linkedin {
  background-color: rgba(10, 102, 194, 0.2);
  color: #0A66C2;
}

body.dark-theme .social-icon.github {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

body.dark-theme .social-icon.twitter {
  background-color: rgba(29, 161, 242, 0.2);
  color: #1DA1F2;
}

body.dark-theme .social-icon:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Scroll Indicator Styles */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.5s ease, transform 0.3s ease;
  animation: fadeInUp 1.5s ease-in-out 1s forwards;
  /* Make the touch target larger */
  padding: 15px;
  margin: -15px;
  /* Ensure it's clickable */
  pointer-events: auto;
  /* Ensure it's visible on the homepage */
  will-change: opacity, transform;
}

.scroll-indicator:hover {
  opacity: 1;
  transform: translateX(-50%) translateY(-5px);
}

.scroll-indicator:active {
  opacity: 0.6;
  transform: translateX(-50%) translateY(0);
}

.scroll-arrow {
  width: 20px;
  height: 12px;
  position: relative;
  margin-bottom: 8px;
  animation: bounce 2s infinite;
}

.scroll-arrow:before,
.scroll-arrow:after {
  content: '';
  position: absolute;
  top: 0;
  width: 12px;
  height: 2px;
  background-color: hsl(var(--primary));
}

.scroll-arrow:before {
  left: 0;
  transform: rotate(45deg);
}

.scroll-arrow:after {
  right: 0;
  transform: rotate(-45deg);
}

/* Dark mode styles for scroll indicator */
body.dark-theme .scroll-arrow:before,
body.dark-theme .scroll-arrow:after {
  background-color: hsl(var(--primary));
}

.scroll-text {
  font-size: 0.8rem;
  color: hsl(var(--muted-foreground));
  font-weight: 500;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-top: 4px;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(10px);
  }
  60% {
    transform: translateY(5px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 0.8;
    transform: translateX(-50%) translateY(0);
  }
}

/* Mobile responsiveness for introduction section */
@media screen and (max-width: 992px) {
  .introduction {
    padding: 30px 20px;
  }

  .intro-heading {
    font-size: 3rem;
  }

  .bio-text {
    font-size: 1rem;
  }
}

@media screen and (max-width: 768px) {
  .introduction {
    flex-direction: column-reverse;
    padding: 20px;
    gap: 30px;
  }

  .introduction .image-container {
    width: 100%;
    margin-left: 0;
    display: flex;
    justify-content: center;
  }

  .introduction .text-container {
    width: 100%;
    margin-right: 0;
    padding-left: 5px; /* Small padding for mobile view */
    text-align: center;
  }

  /* Adjust image size for mobile */
  .morph-container {
    width: 250px;
    height: 250px;
  }

  .intro-heading {
    font-size: 2.5rem;
  }

  .bio-title {
    font-size: 1.2rem;
  }

  .bio-text {
    margin: 0 auto;
  }

  /* Center buttons on mobile */
  .button-container {
    justify-content: center;
    margin-left: 0; /* Reset margin for mobile */
    padding-left: 0; /* Reset padding for mobile */
  }

  /* Align social icons with content on mobile */
  .social-icons {
    justify-content: center;
    margin-top: 50px; /* Maintain increased spacing on mobile */
    margin-left: 0;
    padding-left: 0; /* Remove padding completely for mobile to ensure proper centering */
    width: 100%; /* Full width on mobile */
    transform: translateX(0); /* Reset the transform on mobile */
  }

  /* Adjust scroll indicator for mobile */
  .scroll-indicator {
    bottom: 20px;
  }

  .scroll-arrow {
    width: 16px;
    height: 10px;
  }

  .scroll-arrow:before,
  .scroll-arrow:after {
    width: 10px;
    height: 1.5px;
  }

  .scroll-text {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .introduction {
    padding: 20px 15px;
  }

  .intro-heading {
    font-size: 2rem;
  }

  .bio-title {
    font-size: 1.1rem;
  }

  .bio-text {
    font-size: 0.95rem;
  }

  .button {
    padding: 12px 20px;
    font-size: 0.9rem;
  }

  .button-container {
    flex-direction: column;
    gap: 15px;
    width: 100%;
    max-width: 250px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 0; /* Ensure no padding on small screens */
  }

  .morph-container {
    width: 200px;
    height: 200px;
  }

  .social-icons {
    margin-top: 40px; /* Slightly less spacing for smallest screens but still increased */
  }

  .social-icon {
    width: 38px; /* Slightly larger than original mobile size */
    height: 38px; /* Slightly larger than original mobile size */
    font-size: 1.1rem;
    margin-right: 0;
  }

  /* Further adjust scroll indicator for smallest screens */
  .scroll-indicator {
    bottom: 15px;
  }

  .scroll-arrow {
    width: 14px;
    height: 8px;
    margin-bottom: 5px;
  }

  .scroll-arrow:before,
  .scroll-arrow:after {
    width: 8px;
    height: 1.5px;
  }

  .scroll-text {
    font-size: 0.65rem;
  }
}
