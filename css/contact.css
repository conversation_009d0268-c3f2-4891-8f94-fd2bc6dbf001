/* Contact Section Styles */
#contact {
  background-color: hsl(var(--background));
  position: relative;
  overflow: hidden;
  min-height: 100vh;
}

#contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 90% 10%, hsla(var(--primary), 0.05) 0%, transparent 70%);
  z-index: 0;
}

#contact .section-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  position: relative;
  z-index: 1;
}

#contact .contact-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
}

/* Contact Top Section */
#contact .contact-top {
  text-align: center;
  padding: 40px 0;
  margin-bottom: 40px;
}

#contact .contact-top h1 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--secondary)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-transform: uppercase;
  letter-spacing: 1px;
}

#contact .contact-top p {
  font-size: 1.1rem;
  color: hsl(var(--muted-foreground));
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Contact Form Container */
.contact-form-container {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 40px;
  margin-bottom: 40px;
}

/* Contact Info */
.contact-info {
  background-color: hsla(var(--card), 0.5);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid hsl(var(--border));
  display: flex;
  flex-direction: column;
}

.contact-info h3 {
  font-size: 1.5rem;
  margin-bottom: 25px;
  color: hsl(var(--foreground));
  position: relative;
  display: inline-block;
}

.contact-info h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: hsl(var(--primary));
  border-radius: 2px;
}

.contact-info-item {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.contact-info-item i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: hsla(var(--primary), 0.1);
  color: hsl(var(--primary));
  border-radius: 50%;
  font-size: 1.2rem;
}

.contact-info-item a,
.contact-info-item span {
  color: hsl(var(--foreground));
  text-decoration: none;
  font-size: 1rem;
  transition: color 0.3s ease;
}

.contact-info-item a:hover {
  color: hsl(var(--primary));
}

.contact-info .social-icons {
  display: flex;
  gap: 15px;
  margin: 30px 0;
}

.contact-info .social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: hsla(var(--primary), 0.1);
  color: hsl(var(--primary));
  border-radius: 50%;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.contact-info .social-icon:hover {
  transform: translateY(-5px);
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Specific classes for different social icons */
.contact-info .social-icon.linkedin {
  background-color: rgba(10, 102, 194, 0.1);
  color: #0A66C2;
}

.contact-info .social-icon.github {
  background-color: rgba(36, 41, 46, 0.1);
  color: #24292E;
}

.contact-info .social-icon.twitter {
  background-color: rgba(29, 161, 242, 0.1);
  color: #1DA1F2;
}

/* Dark mode styles for social icons */
body.dark-theme .contact-info .social-icon.linkedin {
  background-color: rgba(10, 102, 194, 0.2);
  color: #0A66C2;
}

body.dark-theme .contact-info .social-icon.github {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

body.dark-theme .contact-info .social-icon.twitter {
  background-color: rgba(29, 161, 242, 0.2);
  color: #1DA1F2;
}

/* Hover effects for all social icons in dark mode */
body.dark-theme .contact-info .social-icon:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.resume-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: hsla(var(--primary), 0.1);
  color: hsl(var(--primary));
  border: none;
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: auto;
}

.resume-button:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Contact Form */
.contact-form {
  background-color: hsla(var(--card), 0.5);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid hsl(var(--border));
}

.contact-form h3 {
  font-size: 1.5rem;
  margin-bottom: 25px;
  color: hsl(var(--foreground));
  position: relative;
  display: inline-block;
}

.contact-form h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: hsl(var(--primary));
  border-radius: 2px;
}

.contact-form .form-group {
  margin-bottom: 20px;
}

.contact-form label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

.contact-form input,
.contact-form textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  background-color: hsla(var(--background), 0.8);
  color: hsl(var(--foreground));
  font-size: 1rem;
  transition: all 0.3s ease;
}

.contact-form textarea {
  min-height: 150px;
  resize: vertical;
}

.contact-form input:focus,
.contact-form textarea:focus {
  outline: none;
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 2px hsla(var(--primary), 0.2);
}

.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  border: none;
  border-radius: 8px;
  padding: 14px 30px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  margin-top: 10px;
}

.submit-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.submit-button:active {
  transform: translateY(-1px);
}

.submit-button i {
  font-size: 1.1rem;
  transition: transform 0.3s ease;
}

.submit-button:hover i {
  transform: translateX(5px);
}

/* Contact Footer */
.contact-footer {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid hsla(var(--border), 0.5);
  margin-top: 20px;
}

.contact-footer p {
  color: hsl(var(--muted-foreground));
  font-size: 0.9rem;
}

/* Email and Social Links */
.email-link {
  font-size: 1.5em;
  font-weight: 700;
  margin-bottom: 15px;
  display: inline-block;
  color: hsl(var(--primary-foreground));
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.email-link:hover {
  text-shadow: 0 0 8px hsl(var(--primary));
}

.email-link::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -5px;
  left: 0;
  background-color: hsl(var(--primary-foreground));
  transform: scaleX(0);
  transform-origin: bottom right;
  transition: transform 0.3s ease;
}

.email-link:hover::after {
  transform: scaleX(1);
  transform-origin: bottom left;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 25px;
  margin-top: 10px;
}

.social-icon {
  color: hsl(var(--primary-foreground));
  font-size: 1.8em;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
}

.social-icon:hover {
  transform: translateY(-5px) scale(1.1);
  background-color: rgba(255, 255, 255, 0.2);
}

/* Animated background particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background-color: hsla(var(--primary-foreground), 0.3);
  pointer-events: none;
  animation: float 15s infinite ease-in-out;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-20px) translateX(10px);
  }
  50% {
    transform: translateY(-10px) translateX(20px);
  }
  75% {
    transform: translateY(-30px) translateX(-10px);
  }
}

/* Responsive styles */
@media (max-width: 992px) {
  .contact-form-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .contact-info {
    order: 2;
  }

  .contact-form {
    order: 1;
  }
}

@media (max-width: 768px) {
  #contact .contact-top {
    padding: 30px 0;
  }

  #contact .contact-top h1 {
    font-size: 2rem;
  }

  #contact .contact-top p {
    font-size: 1rem;
  }

  .contact-info-item span {
    font-size: 0.9rem;
  }

  .contact-info-item i {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .contact-info .social-icon {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .contact-form h3,
  .contact-info h3 {
    font-size: 1.3rem;
  }
}

@media (max-width: 480px) {
  #contact .contact-top h1 {
    font-size: 1.7rem;
  }

  #contact .contact-top p {
    font-size: 0.9rem;
  }

  .contact-form,
  .contact-info {
    padding: 20px;
  }

  .contact-info-item {
    gap: 10px;
  }

  .contact-info .social-icons {
    gap: 10px;
  }

  .contact-form label {
    font-size: 0.85rem;
  }

  .contact-form input,
  .contact-form textarea {
    padding: 10px 12px;
    font-size: 0.9rem;
  }

  .submit-button {
    padding: 12px 20px;
    font-size: 0.9rem;
  }
}

/* Dark mode styles */
body.dark-theme .contact-info,
body.dark-theme .contact-form {
  background-color: hsla(var(--card), 0.2);
  border-color: hsla(var(--border), 0.3);
}

body.dark-theme .contact-info-item i,
body.dark-theme .contact-info .social-icon,
body.dark-theme .resume-button {
  background-color: hsla(var(--primary), 0.2);
}

body.dark-theme .contact-form input,
body.dark-theme .contact-form textarea {
  background-color: hsla(var(--background), 0.5);
  border-color: hsla(var(--border), 0.5);
}
